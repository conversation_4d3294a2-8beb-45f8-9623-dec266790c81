// socket-server/src/services/encryptionContext.ts
/**
 * EncryptionContext class for dynamic key generation and management
 * Implements Double Ratchet protocol for end-to-end encryption
 */

import { PrismaClient } from '@prisma/client';
import { randomBytes, createHash } from 'crypto';
import { EncryptionService } from './encryptionService';

export interface EncryptionKeys {
  encryptedContent: string;
  iv: string;
  senderRatchetKey: string;
  messageNumber: number;
  previousChainLength: number;
}

export interface SessionState {
  rootKey: string;
  chainKeySend?: string;
  chainKeyReceive?: string;
  messageNumberSend: number;
  messageNumberReceive: number;
  previousChainLength: number;
  senderRatchetKeyPair?: {
    publicKey: string;
    privateKey: string;
  };
}

export class EncryptionContext {
  private prisma: PrismaClient;
  private encryptionService: EncryptionService;

  constructor(prisma: PrismaClient) {
    this.prisma = prisma;
    this.encryptionService = new EncryptionService(prisma);
  }

  /**
   * Generate encryption keys for a message
   * This simulates the Double Ratchet protocol key generation
   */
  async generateMessageKeys(
    conversationId: string,
    senderId: string,
    content: string
  ): Promise<EncryptionKeys> {
    try {
      // Get or create session for this conversation and sender
      let session = await this.encryptionService.getConversationSession(
        conversationId,
        senderId
      );

      if (!session) {
        // Initialize new session
        session = await this.initializeSession(conversationId, senderId);
      }

      // Parse session state
      const sessionState = this.parseSessionState(session?.sessionState);

      // Generate new ratchet key pair for this message
      const senderRatchetKeyPair = this.generateKeyPair();
      
      // Derive message key from chain key
      const messageKey = this.deriveMessageKey(
        sessionState.chainKeySend || this.generateChainKey(),
        sessionState.messageNumberSend
      );

      // Generate IV for this message
      const iv = this.generateIV();

      // Encrypt the content
      const encryptedContent = this.encryptMessage(content, messageKey, iv);

      // Update session state
      const updatedSessionState: SessionState = {
        ...sessionState,
        messageNumberSend: sessionState.messageNumberSend + 1,
        chainKeySend: this.advanceChainKey(sessionState.chainKeySend || this.generateChainKey()),
        senderRatchetKeyPair,
      };

      // Save updated session
      await this.encryptionService.upsertConversationSession(
        conversationId,
        senderId,
        {
          sessionState: updatedSessionState,
          rootKey: session?.rootKey || this.generateRootKey(),
          chainKeySend: updatedSessionState.chainKeySend,
          chainKeyReceive: sessionState.chainKeyReceive,
          messageNumberSend: updatedSessionState.messageNumberSend,
          messageNumberReceive: sessionState.messageNumberReceive,
          previousChainLength: sessionState.previousChainLength,
        }
      );

      return {
        encryptedContent,
        iv,
        senderRatchetKey: senderRatchetKeyPair.publicKey,
        messageNumber: sessionState.messageNumberSend,
        previousChainLength: sessionState.previousChainLength,
      };

    } catch (error) {
      console.error('Error generating message keys:', error);
      throw new Error('Failed to generate encryption keys');
    }
  }

  /**
   * Initialize a new encryption session for a conversation participant
   */
  private async initializeSession(
    conversationId: string,
    participantId: string
  ): Promise<any> {
    const initialSessionState: SessionState = {
      rootKey: this.generateRootKey(),
      chainKeySend: this.generateChainKey(),
      messageNumberSend: 0,
      messageNumberReceive: 0,
      previousChainLength: 0,
      senderRatchetKeyPair: this.generateKeyPair(),
    };

    return await this.encryptionService.upsertConversationSession(
      conversationId,
      participantId,
      {
        sessionState: initialSessionState,
        rootKey: initialSessionState.rootKey,
        chainKeySend: initialSessionState.chainKeySend,
        messageNumberSend: 0,
        messageNumberReceive: 0,
        previousChainLength: 0,
      }
    );
  }

  /**
   * Parse session state from database JSON
   */
  private parseSessionState(sessionState: any): SessionState {
    if (typeof sessionState === 'object' && sessionState !== null) {
      return {
        rootKey: sessionState.rootKey || this.generateRootKey(),
        chainKeySend: sessionState.chainKeySend,
        chainKeyReceive: sessionState.chainKeyReceive,
        messageNumberSend: sessionState.messageNumberSend || 0,
        messageNumberReceive: sessionState.messageNumberReceive || 0,
        previousChainLength: sessionState.previousChainLength || 0,
        senderRatchetKeyPair: sessionState.senderRatchetKeyPair,
      };
    }

    // Return default session state
    return {
      rootKey: this.generateRootKey(),
      messageNumberSend: 0,
      messageNumberReceive: 0,
      previousChainLength: 0,
    };
  }

  /**
   * Generate a new key pair for ratcheting
   */
  private generateKeyPair(): { publicKey: string; privateKey: string } {
    // In a real implementation, this would use proper ECDH key generation
    // For now, we'll simulate with base64 encoded random data
    const privateKey = randomBytes(32);
    const publicKey = createHash('sha256').update(privateKey).digest();
    
    return {
      publicKey: publicKey.toString('base64'),
      privateKey: privateKey.toString('base64'),
    };
  }

  /**
   * Generate a root key for the session
   */
  private generateRootKey(): string {
    return randomBytes(32).toString('base64');
  }

  /**
   * Generate a chain key
   */
  private generateChainKey(): string {
    return randomBytes(32).toString('base64');
  }

  /**
   * Generate an initialization vector
   */
  private generateIV(): string {
    return randomBytes(16).toString('base64');
  }

  /**
   * Derive message key from chain key and message number
   */
  private deriveMessageKey(chainKey: string, messageNumber: number): string {
    const input = Buffer.concat([
      Buffer.from(chainKey, 'base64'),
      Buffer.from(messageNumber.toString()),
    ]);
    return createHash('sha256').update(input).digest('base64');
  }

  /**
   * Advance the chain key (ratchet forward)
   */
  private advanceChainKey(chainKey: string): string {
    const hash = createHash('sha256').update(Buffer.from(chainKey, 'base64')).digest();
    return hash.toString('base64');
  }

  /**
   * Encrypt message content (simplified implementation)
   */
  private encryptMessage(content: string, messageKey: string, iv: string): string {
    // In a real implementation, this would use AES-GCM or similar
    // For now, we'll use a simple XOR with the message key for demonstration
    const contentBuffer = Buffer.from(content, 'utf8');
    const keyBuffer = Buffer.from(messageKey, 'base64');
    const ivBuffer = Buffer.from(iv, 'base64');
    
    const encrypted = Buffer.alloc(contentBuffer.length);
    for (let i = 0; i < contentBuffer.length; i++) {
      encrypted[i] = contentBuffer[i] ^ keyBuffer[i % keyBuffer.length] ^ ivBuffer[i % ivBuffer.length];
    }
    
    return encrypted.toString('base64');
  }

  /**
   * Check if a conversation supports encryption
   */
  async isConversationEncryptionReady(conversationId: string): Promise<boolean> {
    return await this.encryptionService.isConversationEncrypted(conversationId);
  }

  /**
   * Get encryption status for conversation participants
   */
  async getConversationEncryptionStatus(conversationId: string) {
    return await this.encryptionService.getConversationParticipants(conversationId);
  }
}
